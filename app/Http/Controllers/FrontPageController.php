<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Article;
use App\Models\Page;

class FrontPageController extends Controller
{
    public function __invoke()
    {
        $path = request()->path();
        $slug = str_replace('/', '.', $path);

        // Try to find page in CMS first
        $page = Page::published()
                   ->bySlug($slug)
                   ->first();

        if ($page) {
            $translation = $page->getTranslation();

            if (!$translation) {
                // If no translation found for current locale, try fallback or 404
                if (app()->currentLocale() !== 'en') {
                    $translation = $page->getTranslation('en');
                }

                if (!$translation) {
                    abort(404);
                }
            }

            return view('front.cms.page', compact('page', 'translation'));
        }

        // Fallback to old hardcoded system for backward compatibility
        return $this->handleLegacyPages($path, $slug);
    }

    private function handleLegacyPages($path, $slug)
    {
        // Legacy special case handling
        if (
            $slug === 'treatment-center.therapy.our-philosophy' ||
            $slug === 'treatment-center.therapy.integrative-therapies'
        ) {
            if(app()->currentLocale() === 'fr') {
                return redirect('/');
            } else {
                switch(app()->currentLocale()) {
                    case 'en':
                        return view('front.en.'.$slug);
                    break;
                    case 'de':
                        return view('front.de.'.$slug);
                    break;
                    default:
                        return redirect('/');
                    break;
                }
            }
        }

        if ($slug === 'treatment-center.analysis-methods.hypoxie-test') {
            if(app()->currentLocale() === 'fr') {
                return view('front.fr.'.$slug);
            } else {
                return redirect('/');
            }
        }

        // Try to load legacy view files
        switch(app()->currentLocale()) {
            case 'en':
                if (view()->exists('front.en.'.$slug)) {
                    return view('front.en.'.$slug);
                }
                break;
            case 'de':
                if (view()->exists('front.de.'.$slug)) {
                    return view('front.de.'.$slug);
                }
                break;
            case 'fr':
                if (view()->exists('front.fr.'.$slug)) {
                    return view('front.fr.'.$slug);
                }
                break;
        }

        // If no view found, return 404
        abort(404);
    }
}
